import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:digital_cv_mobile/components/posisi_pekerjaan_dialog.dart';
import 'package:digital_cv_mobile/controllers/location_controller.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Test Posisi Dialog',
      home: TestScreen(),
    );
  }
}

class TestScreen extends StatelessWidget {
  final LocationController locationController = Get.put(LocationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Posisi Pekerjaan Dialog'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () async {
                final result = await showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) => const PosisiPekerjaanDialog(),
                );

                if (result != null) {
                  Get.snackbar(
                    'Posisi Dipilih',
                    'Anda memilih: ${result.text}',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                }
              },
              child: Text('Buka Dialog Posisi Pekerjaan'),
            ),
            SizedBox(height: 20),
            Container(
              padding: EdgeInsets.all(16),
              margin: EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                children: [
                  Text(
                    '🚀 Fitur Auto-Add Terbaru',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Ketik nama posisi untuk mencari\n'
                    '• Jika tidak ditemukan, otomatis ditambahkan ke list\n'
                    '• Tidak perlu klik tombol tambah\n'
                    '• Posisi baru ditandai dengan label "BARU"\n'
                    '• Debounce 500ms untuk performa optimal',
                    textAlign: TextAlign.left,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
