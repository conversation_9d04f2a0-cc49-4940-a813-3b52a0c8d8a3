import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:digital_cv_mobile/components/posisi_pekerjaan_dialog.dart';
import 'package:digital_cv_mobile/controllers/location_controller.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Test Posisi Dialog',
      home: TestScreen(),
    );
  }
}

class TestScreen extends StatelessWidget {
  final LocationController locationController = Get.put(LocationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Posisi Pekerjaan Dialog'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () async {
                final result = await showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) => const PosisiPekerjaanDialog(),
                );
                
                if (result != null) {
                  Get.snackbar(
                    'Posisi Dipilih',
                    'Anda memilih: ${result.text}',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                }
              },
              child: Text('Buka Dialog Posisi Pekerjaan'),
            ),
            SizedBox(height: 20),
            Text(
              'Fitur yang tersedia:\n'
              '• Cari posisi pekerjaan\n'
              '• Tambah posisi baru jika tidak ditemukan\n'
              '• Auto-select posisi yang baru ditambahkan\n'
              '• Indikator untuk posisi yang baru ditambahkan',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
