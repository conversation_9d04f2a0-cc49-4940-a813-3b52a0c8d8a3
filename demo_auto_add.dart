import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:digital_cv_mobile/components/posisi_pekerjaan_dialog.dart';
import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:digital_cv_mobile/models/posisi_pekerjaan_model.dart';

void main() {
  runApp(DemoApp());
}

class DemoApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Demo Auto-Add Posisi',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: DemoScreen(),
    );
  }
}

class DemoScreen extends StatefulWidget {
  @override
  _DemoScreenState createState() => _DemoScreenState();
}

class _DemoScreenState extends State<DemoScreen> {
  final LocationController locationController = Get.put(LocationController());
  PosisiPekerjaanModel? selectedPosition;
  List<String> searchHistory = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🚀 Demo Auto-Add Posisi'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Info
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue[50]!, Colors.blue[100]!],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✨ Fitur Auto-Add Terbaru',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Ketik nama posisi → Otomatis ditambahkan jika tidak ditemukan!',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 24),
            
            // Demo Button
            Container(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _showPosisiDialog,
                icon: Icon(Icons.search, color: Colors.white),
                label: Text(
                  'Buka Dialog Posisi Pekerjaan',
                  style: TextStyle(color: Colors.white, fontSize: 16),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            // Instructions
            _buildInstructionCard(),
            
            SizedBox(height: 24),
            
            // Selected Position Display
            if (selectedPosition != null) _buildSelectedPositionCard(),
            
            SizedBox(height: 24),
            
            // Search History
            if (searchHistory.isNotEmpty) _buildSearchHistoryCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.green[600], size: 20),
              SizedBox(width: 8),
              Text(
                'Cara Menggunakan',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          _buildInstructionStep('1', 'Klik tombol "Buka Dialog" di atas'),
          _buildInstructionStep('2', 'Ketik nama posisi (contoh: "Data Scientist")'),
          _buildInstructionStep('3', 'Tunggu 500ms (debounce)'),
          _buildInstructionStep('4', 'Posisi otomatis muncul dengan label "BARU"'),
          _buildInstructionStep('5', 'Klik untuk memilih'),
        ],
      ),
    );
  }

  Widget _buildInstructionStep(String number, String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.green[600],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.green[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedPositionCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.check_circle, color: Colors.orange[600], size: 20),
              SizedBox(width: 8),
              Text(
                'Posisi Terpilih',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        selectedPosition!.text,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    if (selectedPosition!.id.startsWith('temp_'))
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'BARU',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: 8),
                Text('ID: ${selectedPosition!.id}'),
                Text('Status: ${selectedPosition!.id.startsWith('temp_') ? 'Posisi Baru (Auto-Added)' : 'Dari Database'}'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHistoryCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.history, color: Colors.purple[600], size: 20),
              SizedBox(width: 8),
              Text(
                'Riwayat Pencarian',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          ...searchHistory.map((search) => Container(
            width: double.infinity,
            margin: EdgeInsets.only(bottom: 4),
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.purple[200]!),
            ),
            child: Text(search),
          )).toList(),
        ],
      ),
    );
  }

  void _showPosisiDialog() async {
    final result = await showModalBottomSheet<PosisiPekerjaanModel>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const PosisiPekerjaanDialog(),
    );

    if (result != null) {
      setState(() {
        selectedPosition = result;
        searchHistory.add('${result.text} - ${DateTime.now().toString().substring(11, 19)}');
        if (searchHistory.length > 5) {
          searchHistory.removeAt(0); // Keep only last 5
        }
      });

      // Show success feedback
      Get.snackbar(
        result.id.startsWith('temp_') ? '🎉 Posisi Baru Ditambahkan!' : '✅ Posisi Dipilih',
        result.id.startsWith('temp_') 
            ? 'Posisi "${result.text}" berhasil ditambahkan secara otomatis!'
            : 'Anda memilih posisi "${result.text}"',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: result.id.startsWith('temp_') ? Colors.green : Colors.blue,
        colorText: Colors.white,
        duration: Duration(seconds: 3),
      );
    }
  }
}
