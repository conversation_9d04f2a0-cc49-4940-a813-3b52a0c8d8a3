import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/services/form_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FormController extends GetxController {
  final FormService formService = FormService();
  final snackbar = Get.find<SnackBarService>();
  late SharedPreferences prefs;
  final secureStorage = Get.put(FlutterSecureStorage());
  String pin = "";
  var isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    prefs = Get.find<SharedPreferences>();
  }

  @override
  void onClose() {
    super.onClose();
    Get.delete<FormController>();
  }

  Future<bool> saveInfoPribadi(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.saveInfoPribadi(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              // Pastikan semua dialog tertutup sebelum navigasi dengan delay kecil
              await Future.delayed(Duration(milliseconds: 100));
              while (Get.isDialogOpen == true) {
                Get.back();
                await Future.delayed(Duration(milliseconds: 50));
              }
              while (Get.isBottomSheetOpen == true) {
                Get.back();
                await Future.delayed(Duration(milliseconds: 50));
              }
              Get.back(result: true);
              snackbar.showSuccess(message);
              return true;
            } else {
              snackbar.showError("controller.update_failed".tr);
              return false;
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              // Pastikan semua dialog tertutup sebelum navigasi dengan delay kecil
              await Future.delayed(Duration(milliseconds: 100));
              while (Get.isDialogOpen == true) {
                Get.back();
                await Future.delayed(Duration(milliseconds: 50));
              }
              while (Get.isBottomSheetOpen == true) {
                Get.back();
                await Future.delayed(Duration(milliseconds: 50));
              }
              Get.back(result: true);
              snackbar.showSuccess(message);
              return true;
            } else {
              snackbar.showError("controller.update_failed".tr);
              return false;
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return false;
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return false;
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
      return false;
    } finally {
      isLoading(false);
    }
  }

  Future<bool> saveDataCVATS(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);

      var response = await formService.saveDataCVATS(data);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              // Pastikan semua dialog tertutup sebelum navigasi dengan delay kecil
              await Future.delayed(Duration(milliseconds: 100));
              while (Get.isDialogOpen == true) {
                Get.back();
                await Future.delayed(Duration(milliseconds: 50));
              }
              while (Get.isBottomSheetOpen == true) {
                Get.back();
                await Future.delayed(Duration(milliseconds: 50));
              }
              Get.back(result: true);
              snackbar.showSuccess(message);
              return true;
            } else {
              snackbar.showError("controller.update_failed".tr);
              return false;
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              // Pastikan semua dialog tertutup sebelum navigasi dengan delay kecil
              await Future.delayed(Duration(milliseconds: 100));
              while (Get.isDialogOpen == true) {
                Get.back();
                await Future.delayed(Duration(milliseconds: 50));
              }
              while (Get.isBottomSheetOpen == true) {
                Get.back();
                await Future.delayed(Duration(milliseconds: 50));
              }
              Get.back(result: true);
              snackbar.showSuccess(message);
              return true;
            } else {
              snackbar.showError("controller.update_failed".tr);
              return false;
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return false;
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return false;
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
      return false;
    } finally {
      isLoading(false);
    }
  }

  Future<void> saveRiwayatPendidikan(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.saveRiwayatPendidikan(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showSuccess("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showSuccess("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");

          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateRiwayatPendidikan(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.updateRiwayatPendidikan(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showSuccess("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showSuccess("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> deleteRiwayatPendidikan(String idRiwayat) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.deleteRiwayatPendidikan(idRiwayat, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.delete_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.delete_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.delete_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.delete_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> saveRiwayatKursus(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.saveRiwayatKursus(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateRiwayatKursus(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.updateRiwayatKursus(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> deleteRiwayatKursus(String idRiwayat) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.deleteRiwayatKursus(idRiwayat, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.delete_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.delete_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.delete_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.delete_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> saveRiwayatPekerjaan(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.saveRiwayatPekerjaan(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateRiwayatPekerjaan(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.updateRiwayatPekerjaan(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> deleteRiwayatPekerjaan(String idRiwayat) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.deleteRiwayatPekerjaan(idRiwayat, pin);

      if (response.statusCode == 200) {
        LogService.log.i("Response data: ${response.data}");
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.delete_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.delete_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.delete_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.delete_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> saveInfoPekerjaan(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.saveInfoPekerjaan(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> saveRiwayatOrganisasi(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.saveRiwayatOrganisasi(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> updateRiwayatOrganisasi(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.updateRiwayatOrganisasi(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> deleteRiwayatOrganisasi(String idRiwayat) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.deleteRiwayatOrganisasi(idRiwayat, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.delete_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.delete_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.delete_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.delete_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> saveRiwayatMinatKonsep(List<Map<String, dynamic>> data) async {
    try {
      isLoading(true);
      pin = await secureStorage.read(key: "key") ?? "";

      var response = await formService.saveMinatKonsep(data, pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;

          if (responseData.containsKey("data")) {
            // Map<String, dynamic> user = responseData["data"];

            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          } else {
            if (responseData.containsKey("status") &&
                responseData["status"] == "success") {
              String message = "controller.update_success".tr;
              Get.back(result: true);
              snackbar.showSuccess(message);
            } else {
              snackbar.showError("controller.update_failed".tr);
            }
          }
        } else {
          snackbar
              .showError("${"controller.invalid_format".tr} FormController");
          LogService.log.e(
              "Error: Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      }
      // 🔹 Menangani error login (400 Bad Request)
      else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
      }
      // 🔹 Menangani status lainnya (contoh: 500 Internal Server Error)
      else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }
}
