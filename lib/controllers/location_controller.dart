import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/lokasi_model.dart';
import 'package:digital_cv_mobile/models/posisi_pekerjaan_model.dart';
import 'package:digital_cv_mobile/services/location_service.dart';
import 'package:get/get.dart';

class LocationController extends GetxController {
  final LocationService locationService = LocationService();
  final snackbar = Get.find<SnackBarService>();
  final RxBool isLoading = false.obs;

  RxList<LokasiModel> lokasiList = <LokasiModel>[].obs;
  RxList<LokasiModel> provinsiList = <LokasiModel>[].obs;
  RxList<LokasiModel> kotaList = <LokasiModel>[].obs;
  RxList<LokasiModel> kecamatanList = <LokasiModel>[].obs;
  RxList<PosisiPekerjaanModel> posisiKerjaanList = <PosisiPekerjaanModel>[].obs;

  Future<void> getPosisiKerjaan(String search, int page, int pageSize) async {
    try {
      isLoading(true);
      var response =
          await locationService.getPosisiKerjaan(search, page, pageSize);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["status"] == "success") {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<PosisiPekerjaanModel> result = dataList
                  .map((item) => PosisiPekerjaanModel.fromJson(item))
                  .toList();
              posisiKerjaanList.assignAll(result);
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              posisiKerjaanList.clear();
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            posisiKerjaanList.clear();
          }
        } else {
          posisiKerjaanList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        posisiKerjaanList.clear();
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        posisiKerjaanList.clear();
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> getLokasi(String search, int page, int pageSize) async {
    try {
      isLoading(true);
      var response = await locationService.getLokasi(search, page, pageSize);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              lokasiList.assignAll(result);
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              lokasiList.clear();
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            lokasiList.clear();
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} locationController");
          lokasiList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        lokasiList.clear();
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        lokasiList.clear();
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> getProvinsi(String search, int page) async {
    try {
      isLoading(true);
      var response = await locationService.getProvinsi(search, page);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              provinsiList.assignAll(result);
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              provinsiList.clear();
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            provinsiList.clear();
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} locationController");
          provinsiList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        provinsiList.clear();
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        provinsiList.clear();
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<String> getIdProvinsi(String search, int page) async {
    try {
      isLoading(true);
      var response = await locationService.getProvinsi(search, page);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              var id = result[0].id.toString();
              return id;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return "";
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            return "";
          }
        } else {
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return "";
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return "";
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        return "";
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
      return "";
    } finally {
      isLoading(false);
    }
  }

  Future<String> getIdKota(String search, int page, int idProvince) async {
    try {
      isLoading(true);
      var response = await locationService.getKota(search, page, idProvince);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              return result[0].id.toString();
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return "";
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            return "";
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} locationController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return "";
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return "";
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        return "";
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
      return "";
    } finally {
      isLoading(false);
    }
  }

  Future<String> getNamaKota(String search, int page, int idProvince) async {
    try {
      isLoading(true);
      var response = await locationService.getKota(search, page, idProvince);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              return result[0].nama.toString();
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return "";
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            return "";
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} locationController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return "";
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return "";
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        return "";
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
      return "";
    } finally {
      isLoading(false);
    }
  }

  Future<String> getIdKecaatan(String search, int page, int idKota) async {
    try {
      isLoading(true);
      var response = await locationService.getKecamatan(search, page, idKota);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              return result[0].id.toString();
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              return "";
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            return "";
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} locationController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return "";
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return "";
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        return "";
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
      return "";
    } finally {
      isLoading(false);
    }
  }

  Future<String> getNamaKecaatan(String search, int page, int idKota) async {
    try {
      isLoading(true);
      var response = await locationService.getKecamatan(search, page, idKota);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              return result[0].nama.toString();
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              return "";
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            return "";
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} locationController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return "";
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return "";
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        return "";
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
      return "";
    } finally {
      isLoading(false);
    }
  }

  Future<void> getKota(String search, int page, int idProvince) async {
    try {
      isLoading(true);
      var response = await locationService.getKota(search, page, idProvince);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              kotaList.assignAll(result);
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              kotaList.clear();
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            kotaList.clear();
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} locationController");
          kotaList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        kotaList.clear();
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        kotaList.clear();
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }

  Future<void> getKecamatan(String search, int page, int idRegency) async {
    try {
      isLoading(true);
      var response =
          await locationService.getKecamatan(search, page, idRegency);
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<LokasiModel> result =
                  dataList.map((item) => LokasiModel.fromJson(item)).toList();
              kecamatanList.assignAll(result);
            } else {
              // snackbar.showError("controller.data_not_found".tr);
              kecamatanList.clear();
            }
          } else {
            // snackbar.showError("controller.show_data_failed".tr);
            kecamatanList.clear();
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} locationController");
          kecamatanList.clear();
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        kecamatanList.clear();
      } else {
        snackbar.showError(
            "${"controller.server_error".tr}: ${response.statusCode}");
        kecamatanList.clear();
      }
    } catch (e) {
      snackbar.showError("$e");
      LogService.log.e("Login error: $e");
    } finally {
      isLoading(false);
    }
  }
}
