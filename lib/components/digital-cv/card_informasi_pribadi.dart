import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/routes/app_route.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CardInformasiPribadi extends StatelessWidget {
  const CardInformasiPribadi({
    super.key,
    required this.infoCVController,
    required this.timelineData,
    required this.profileController,
  });

  final GetInfoCVController infoCVController;
  final List<Map<String, String>> timelineData;
  final ProfileController profileController;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: SizedBox(
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                // ✅ Ganti `Expanded` dengan `Flexible`
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Material(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Obx(
                            () => Skeletonizer(
                              enabled: infoCVController.isLoadingRH.value,
                              child: infoCVController.rhList.isEmpty
                                  ? Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.person,
                                              size: 18,
                                              color: ColorAsset.primaryColor,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              timelineData[0]["group"]!,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        GestureDetector(
                                          onTap: () async {
                                            var result = infoCVController
                                                    .rhList.isNotEmpty
                                                ? await Get.toNamed(
                                                    Routes.infoPribadi,
                                                    arguments: infoCVController
                                                        .rhList[0])
                                                : await Get.toNamed(
                                                    Routes.infoPribadi);
                                            if (result == true) {
                                              infoCVController.loadRH();
                                            }
                                          },
                                          child: Icon(
                                            Icons.add,
                                            size: 18,
                                            color: ColorAsset.secodaryColor,
                                          ),
                                        ),
                                      ],
                                    )
                                  : Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.person,
                                              size: 18,
                                              color: ColorAsset.primaryColor,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              timelineData[0]["group"]!,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Divider(
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: const BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  boxShadow: []),
                                              child: CircleAvatar(
                                                radius: 60,
                                                child: ClipOval(
                                                  child: profileController
                                                              .rxImage.value !=
                                                          ""
                                                      ? Image.network(
                                                          profileController
                                                              .rxImage.value,
                                                          fit: BoxFit.cover,
                                                          width: 50,
                                                          height: 50,
                                                          errorBuilder:
                                                              (context, error,
                                                                  stackTrace) {
                                                            return const Icon(
                                                              Icons.person,
                                                              size: 20,
                                                            );
                                                          },
                                                        )
                                                      : const Icon(
                                                          Icons.person,
                                                          size: 20,
                                                        ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 8),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    infoCVController
                                                            .rhList[0].nama ??
                                                        '-',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                        Icons
                                                            .location_on_outlined,
                                                        size: 14,
                                                        color: Colors.grey[500],
                                                      ),
                                                      SizedBox(width: 4),
                                                      Text(
                                                        infoCVController
                                                                .rhList[0]
                                                                .tempatLahirFormatted ??
                                                            '-',
                                                        style: TextStyle(
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight
                                                                    .normal,
                                                            color: Colors
                                                                .grey[500]),
                                                      )
                                                    ],
                                                  )
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                // LogService.log.i(
                                                //     "INI : ${infoCVController.rhList[0]}");
                                                var result = await Get.toNamed(
                                                    Routes.infoPribadi,
                                                    parameters: {
                                                      "from": key.toString()
                                                    },
                                                    arguments: infoCVController
                                                        .rhList[0]);
                                                if (result == true) {
                                                  infoCVController.loadRH();
                                                }
                                              },
                                              child: Icon(
                                                Icons.edit,
                                                size: 18,
                                                color: ColorAsset.secodaryColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "dcv.info_pribadi.txt_lahir"
                                                      .tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Text(
                                                  infoCVController.rhList[0]
                                                          .tglLahirFormatted ??
                                                      '-',
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.normal,
                                                      color: Colors.grey[500]),
                                                )
                                              ],
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "dcv.info_pribadi.txt_jk".tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Text(
                                                  infoCVController.rhList[0]
                                                          .jenisKelamin ??
                                                      '-',
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.normal,
                                                      color: Colors.grey[500]),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "dcv.info_pribadi.txt_email2"
                                                      .tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Text(
                                                  profileController
                                                      .rxEmail.value,
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.normal,
                                                      color: Colors.grey[500]),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "dcv.info_pribadi.txt_handphone2"
                                                  .tr,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            Text(
                                              profileController.noTelp.value,
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.normal,
                                                  color: Colors.grey[500]),
                                            ),
                                          ],
                                        )
                                        // SizedBox(
                                        //   height: 10,
                                        // ),
                                        // Text(
                                        //   "dcv.info_pribadi.txt_ktp2".tr,
                                        //   style: TextStyle(
                                        //     fontSize: 12,
                                        //     fontWeight: FontWeight.bold,
                                        //   ),
                                        // ),
                                        // Text(
                                        //   infoCVController.rhList[0].ktp,
                                        //   style: TextStyle(
                                        //     fontSize: 12,
                                        //     fontWeight: FontWeight.normal,
                                        //   ),
                                        //   softWrap: true,
                                        // ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
