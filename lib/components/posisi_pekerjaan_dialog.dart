import 'dart:async';
import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:digital_cv_mobile/models/posisi_pekerjaan_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PosisiPekerjaanDialog extends StatefulWidget {
  const PosisiPekerjaanDialog({super.key});

  @override
  State<PosisiPekerjaanDialog> createState() => _PosisiPekerjaanDialogState();
}

class _PosisiPekerjaanDialogState extends State<PosisiPekerjaanDialog> {
  final LocationController controller = Get.find();
  final TextEditingController searchController = TextEditingController();
  String currentSearchQuery = "";
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.posisiKerjaanList.isEmpty) {
        controller.getPosisiKerjaan("", 1, 10);
      }
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      currentSearchQuery = query.trim();
    });

    // Cancel previous timer
    _debounceTimer?.cancel();

    // Set new timer for debouncing
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query.isEmpty) {
        controller.getPosisiKerjaan("", 1, 10);
      } else {
        // Search for existing positions first
        controller.getPosisiKerjaan(query, 1, 10).then((_) {
          // After search is complete, check if we need to add the query as new position
          _checkAndAddNewPosition(query.trim());
        });
      }
    });
  }

  void _checkAndAddNewPosition(String query) {
    if (query.isEmpty) return;

    // Check if the exact query exists in the current list
    bool exactMatch = controller.posisiKerjaanList
        .any((item) => item.text.toLowerCase() == query.toLowerCase());

    // If no exact match and we have a search query, add it as new position
    if (!exactMatch && query.isNotEmpty) {
      final newPosisi = PosisiPekerjaanModel(
        id: "temp_${DateTime.now().millisecondsSinceEpoch}",
        text: query,
      );

      // Add to the beginning of the list
      controller.posisiKerjaanList.insert(0, newPosisi);
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("controller.pilih_posisi_pekerjaan".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "controller.cari_posisi_pekerjaan".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                // If no results at all
                if (controller.posisiKerjaanList.isEmpty) {
                  return Center(
                    child: Text(
                      currentSearchQuery.isNotEmpty
                          ? "Ketik untuk menambahkan posisi baru"
                          : "tidak_ada".tr,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  );
                }

                // Show results
                return ListView.separated(
                  itemCount: controller.posisiKerjaanList.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final posisi = controller.posisiKerjaanList[index];
                    return ListTile(
                      title: Text(posisi.text),
                      onTap: () {
                        Get.back(result: posisi);
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
