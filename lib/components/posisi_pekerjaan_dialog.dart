import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PosisiPekerjaanDialog extends StatefulWidget {
  const PosisiPekerjaanDialog({super.key});

  @override
  State<PosisiPekerjaanDialog> createState() => _PosisiPekerjaanDialogState();
}

class _PosisiPekerjaanDialogState extends State<PosisiPekerjaanDialog> {
  final LocationController controller = Get.find();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.posisiKerjaanList.isEmpty) {
        controller.getPosisiKerjaan("", 1, 10);
      }
    });
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      controller.getPosisiKerjaan("", 1, 10);
    } else {
      controller.getPosisiKerjaan(query, 1, 10);
    }
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("controller.pilih_posisi_pekerjaan".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "controller.cari_posisi_pekerjaan".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                // if (searchController.text.length < 3) {
                //   return const Center(
                //       child: Text("Masukkan minimal 3 karakter"));
                // } else {
                //   if (controller.lokasiList.isEmpty) {
                //     return const Center(child: Text("Tidak ada data"));
                //   }
                // }

                if (controller.posisiKerjaanList.isEmpty) {
                  return Center(child: Text("tidak_ada".tr));
                }

                return ListView.separated(
                  itemCount: controller.posisiKerjaanList.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final posisi = controller.posisiKerjaanList[index];
                    return ListTile(
                      title: Text(posisi.text),
                      onTap: () {
                        Get.back(result: posisi);
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
