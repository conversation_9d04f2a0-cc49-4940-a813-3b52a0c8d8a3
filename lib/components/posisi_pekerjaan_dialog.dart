import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:digital_cv_mobile/models/posisi_pekerjaan_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PosisiPekerjaanDialog extends StatefulWidget {
  const PosisiPekerjaanDialog({super.key});

  @override
  State<PosisiPekerjaanDialog> createState() => _PosisiPekerjaanDialogState();
}

class _PosisiPekerjaanDialogState extends State<PosisiPekerjaanDialog> {
  final LocationController controller = Get.find();
  final TextEditingController searchController = TextEditingController();
  String currentSearchQuery = "";

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.posisiKerjaanList.isEmpty) {
        controller.getPosisiKerjaan("", 1, 10);
      }
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      currentSearchQuery = query.trim();
    });

    if (query.isEmpty) {
      controller.getPosisiKerjaan("", 1, 10);
    } else {
      controller.getPosisiKerjaan(query, 1, 10);
    }
  }

  void _addNewPosisi(String posisiName) {
    // Create a new PosisiPekerjaanModel with a temporary ID
    final newPosisi = PosisiPekerjaanModel(
      id: "temp_${DateTime.now().millisecondsSinceEpoch}",
      text: posisiName,
    );

    // Add to the list temporarily
    controller.posisiKerjaanList.insert(0, newPosisi);

    // Return the new position
    Get.back(result: newPosisi);
  }

  @override
  Widget build(BuildContext context) {
    final double sheetHeight = MediaQuery.of(context).size.height * 0.5;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        height: sheetHeight,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text("controller.pilih_posisi_pekerjaan".tr,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: TextField(
                controller: searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: "controller.cari_posisi_pekerjaan".tr,
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.isTrue) {
                  return const Center(child: CircularProgressIndicator());
                }

                // Check if we have search query but no results
                bool hasSearchQuery = currentSearchQuery.isNotEmpty;
                bool hasResults = controller.posisiKerjaanList.isNotEmpty;
                bool showAddOption = hasSearchQuery && !hasResults;

                // If we have search query but no results, show add option
                if (showAddOption) {
                  return Column(
                    children: [
                      Expanded(
                        child: Center(
                          child: Text(
                            "Tidak ada hasil untuk '$currentSearchQuery'",
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(bottom: 16),
                        child: ElevatedButton.icon(
                          onPressed: () => _addNewPosisi(currentSearchQuery),
                          icon: const Icon(Icons.add, color: Colors.white),
                          label: Text(
                            "Tambah '$currentSearchQuery'",
                            style: const TextStyle(color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                }

                // If no search query and no results
                if (!hasSearchQuery && !hasResults) {
                  return Center(
                    child: Text(
                      "tidak_ada".tr,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  );
                }

                // Show results with optional add button at the top
                return Column(
                  children: [
                    // Show add option at the top if we have search query
                    if (hasSearchQuery && hasResults) ...[
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(bottom: 8),
                        child: OutlinedButton.icon(
                          onPressed: () => _addNewPosisi(currentSearchQuery),
                          icon: const Icon(Icons.add, size: 18),
                          label: Text("Tambah '$currentSearchQuery'"),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            side: const BorderSide(color: Colors.blue),
                            foregroundColor: Colors.blue,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                      const Divider(height: 1),
                    ],
                    // Show existing results
                    Expanded(
                      child: ListView.separated(
                        itemCount: controller.posisiKerjaanList.length,
                        separatorBuilder: (_, __) => const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final posisi = controller.posisiKerjaanList[index];
                          return ListTile(
                            title: Text(posisi.text),
                            trailing: posisi.id.startsWith('temp_')
                                ? const Icon(Icons.new_label,
                                    color: Colors.green, size: 16)
                                : null,
                            onTap: () {
                              Get.back(result: posisi);
                            },
                          );
                        },
                      ),
                    ),
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
