import 'package:digital_cv_mobile/components/digital-cv/card_informasi_pekerjaan.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_informasi_pribadi.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_minat_konsep_pribadi.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_pelatihan_kursus.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_pengalaman_bekerja.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_pengalaman_organisasi.dart';
import 'package:digital_cv_mobile/components/digital-cv/card_riwayat_pendidikan.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/controllers/minat_konsep_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:digital_cv_mobile/helpers/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

class RiwayatHidupScreen extends StatefulWidget {
  const RiwayatHidupScreen({super.key});

  @override
  State<RiwayatHidupScreen> createState() => _RiwayatHidupScreenState();
}

class _RiwayatHidupScreenState extends State<RiwayatHidupScreen> {
  final GetInfoCVController infoCVController = Get.find<GetInfoCVController>();
  final ProfileController profileController = Get.find<ProfileController>();
  final MinatKonsepController minatKonsepController =
      Get.find<MinatKonsepController>();
  bool isCollapsed = false;
  double progress = 0.6; // Contoh progress (60%)

  bool isEmpty = false;

  @override
  void initState() {
    super.initState();
    infoCVController.loadRH();
    infoCVController.loadRiwayatPekerjaan();
    infoCVController.loadRiwayatPendidikan();
    infoCVController.loadRiwayatKursus();
    infoCVController.loadRiwayatOrganisasi();
    infoCVController.loadPenguasaanBahasa();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned.fill(
            child: NotificationListener<ScrollNotification>(
              onNotification: (scrollNotification) {
                if (scrollNotification is ScrollUpdateNotification) {
                  setState(() {
                    isCollapsed = scrollNotification.metrics.pixels >
                        10; // Perubahan lebih cepat
                  });
                }
                return true;
              },
              child: RefreshIndicator(
                onRefresh: () async {
                  infoCVController.analisaCVAI.value = '';
                  infoCVController.loadRH();
                  infoCVController.loadRiwayatPekerjaan();
                  infoCVController.loadRiwayatPendidikan();
                  infoCVController.loadRiwayatKursus();
                  infoCVController.loadRiwayatOrganisasi();
                  infoCVController.loadPenguasaanBahasa();
                },
                child: CustomScrollView(
                  slivers: [
                    SliverAppBar(
                      pinned: true,
                      expandedHeight: 120.0,
                      surfaceTintColor: Colors.transparent,
                      backgroundColor: Colors.white,
                      // shape: RoundedRectangleBorder(
                      //   side: BorderSide(
                      //     color: Colors.black,
                      //   ),
                      // ),
                      forceElevated: true,
                      elevation: 3,
                      shadowColor: Colors.black,
                      iconTheme: IconThemeData(color: Colors.black),
                      title: AnimatedSwitcher(
                        duration: Duration(milliseconds: 300),
                        transitionBuilder: (child, animation) => FadeTransition(
                          opacity: animation,
                          child: child,
                        ),
                        child: isCollapsed
                            ? null // Hilangkan title saat scroll ke bawah
                            : Text(
                                "dcv.riwayat_hidup".tr,
                                key: ValueKey('title'),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                      ),
                      flexibleSpace: Container(
                        padding: EdgeInsets.only(
                            top: MediaQuery.of(context).padding.top),
                        alignment: Alignment.center,
                        child: AnimatedSwitcher(
                          duration: Duration(milliseconds: 300),
                          transitionBuilder: (child, animation) =>
                              FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                          child: isCollapsed
                              ? _buildCollapsedAppBar()
                              : _buildExpandedAppBar(),
                        ),
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 15),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize
                                .min, // ✅ Gunakan `min` agar tidak memaksa ukuran penuh
                            children: [
                              CardInformasiPribadi(
                                  infoCVController: infoCVController,
                                  timelineData: Utilities().timelineData,
                                  profileController: profileController),
                              SizedBox(
                                height: 10,
                              ),
                              CardRiwayatPendidikan(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                              CardPelatihanKursus(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                              CardPengalamanBekerja(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                              CardInformasiPekerjaan(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                              CardPengalamanOrganisasi(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController),
                              SizedBox(
                                height: 10,
                              ),
                              CardMinatdanKonsepPribadi(
                                  timelineData: Utilities().timelineData,
                                  infoCVController: infoCVController,
                                  minatKonsepController: minatKonsepController),
                              SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
              right: 20,
              bottom: 60,
              child: Obx(
                () => Row(
                  children: [
                    if (infoCVController.isLoadingCVAI.value)
                      Container(
                        width: Get.width / 1.8,
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(255, 243, 228, 182),
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: ColorAsset.primaryColor,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.warning_amber_rounded,
                              color: ColorAsset.primaryColor,
                              size: 25,
                            ),
                            SizedBox(width: 10),
                            Flexible(
                              child: Text(
                                "dcv.generate_analisa_info".tr,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    SizedBox(width: 10),
                    SizedBox(
                      height: 50,
                      child: ElevatedButton(
                        onPressed: () async {
                          if (infoCVController.isLoadingCVAI.value) return;
                          if (infoCVController.analisaCVAI.isEmpty) {
                            infoCVController.analisaCVAI.value = '';
                            var result = await infoCVController.generateCVAI();
                            if (result) {
                              showModalBottomSheet(
                                  context: context,
                                  backgroundColor: Colors.white,
                                  builder: (context) => Padding(
                                        padding: const EdgeInsets.all(20),
                                        child: Column(
                                          children: [
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  "dcv.judul_generate_analisa"
                                                      .tr,
                                                  style: TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                IconButton(
                                                  icon: Icon(Icons.close),
                                                  onPressed: () {
                                                    Get.back();
                                                  },
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            Expanded(
                                              child: ListView(
                                                padding: EdgeInsets.zero,
                                                children: [
                                                  Obx(() => Html(
                                                      data: infoCVController
                                                          .analisaCVAI.value))
                                                ],
                                              ),
                                            )
                                          ],
                                        ),
                                      ));
                            }
                          } else {
                            showModalBottomSheet(
                                context: context,
                                backgroundColor: Colors.white,
                                builder: (context) => Padding(
                                      padding: const EdgeInsets.all(20),
                                      child: Column(
                                        children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "dcv.judul_generate_analisa".tr,
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              IconButton(
                                                icon: Icon(Icons.close),
                                                onPressed: () {
                                                  Get.back();
                                                },
                                              ),
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Expanded(
                                            child: ListView(
                                              padding: EdgeInsets.zero,
                                              children: [
                                                Obx(() => Html(
                                                    data: infoCVController
                                                        .analisaCVAI.value))
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    ));
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.zero,
                          backgroundColor: Colors.transparent,
                        ),
                        child: Ink(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: const <Color>[
                                Color(0xFF1976D2),
                                Color.fromARGB(255, 72, 163, 237),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Obx(
                            () => Container(
                              padding:
                                  const EdgeInsets.all(16), // ukuran tombol
                              child: infoCVController.isLoadingCVAI.value
                                  ? SpinKitThreeBounce(
                                      color: ColorAsset.primaryColor,
                                      size: 15,
                                    )
                                  : Row(
                                      children: [
                                        Icon(
                                          Icons.auto_awesome,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                        SizedBox(width: 8),
                                        SizedBox(
                                          width: 70,
                                          child: Text(
                                            infoCVController.analisaCVAI.isEmpty
                                                ? "dcv.generate_ai_analisa".tr
                                                : "dcv.generate_hasil_analisa"
                                                    .tr,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ))
        ],
      ),
    );
  }

  Widget _buildExpandedAppBar() {
    return SingleChildScrollView(
      // Mencegah overflow saat tinggi tidak cukup
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          key: ValueKey('expanded'),
          mainAxisSize:
              MainAxisSize.min, // Pastikan tidak mengambil semua ruang
          children: [
            SizedBox(height: 30),
            Text(
              "dcv.subjudul1".tr,
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              softWrap: true,
            ),
            SizedBox(height: 8),
            _buildProgressIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildCollapsedAppBar() {
    return Padding(
      key: ValueKey('collapsed'),
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(width: 35),
          _buildProgressIndicator(),
          SizedBox(width: 8), // Beri jarak agar tidak terlalu rapat
          Expanded(
            // Memastikan teks tidak menyebabkan overflow
            child: Text(
              "dcv.subjudul2".tr,
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.normal,
              ),
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return SizedBox(
      width: isCollapsed ? 150 : MediaQuery.of(context).size.width,
      height: 5,
      child: Obx(
        () => LinearProgressIndicator(
          value: infoCVController.progress.value,
          borderRadius: BorderRadius.circular(10),
          backgroundColor: Colors.grey[300],
          color: Color(0xFF0D3B72),
        ),
      ),
    );
  }
}
