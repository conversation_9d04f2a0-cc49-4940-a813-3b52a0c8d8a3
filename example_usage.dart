import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:digital_cv_mobile/components/posisi_pekerjaan_dialog.dart';
import 'package:digital_cv_mobile/models/posisi_pekerjaan_model.dart';

class ExampleFormScreen extends StatefulWidget {
  @override
  _ExampleFormScreenState createState() => _ExampleFormScreenState();
}

class _ExampleFormScreenState extends State<ExampleFormScreen> {
  final TextEditingController _posisiController = TextEditingController();
  PosisiPekerjaanModel? selectedPosisi;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Form dengan Auto-Add Posisi'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Posisi Pekerjaan',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            
            // Field untuk menampilkan posisi yang dipilih
            GestureDetector(
              onTap: _showPosisiDialog,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        selectedPosisi?.text ?? 'Pilih posisi pekerjaan',
                        style: TextStyle(
                          color: selectedPosisi != null 
                              ? Colors.black 
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Indikator jika posisi baru
                        if (selectedPosisi?.id.startsWith('temp_') == true)
                          Container(
                            margin: EdgeInsets.only(right: 8),
                            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              'BARU',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Informasi tentang fitur
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue, size: 16),
                      SizedBox(width: 8),
                      Text(
                        'Fitur Auto-Add',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[800],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Cari posisi pekerjaan yang diinginkan\n'
                    '• Jika tidak ditemukan, Anda bisa menambahkan posisi baru\n'
                    '• Posisi baru akan ditandai dengan label "BARU"',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 24),
            
            // Tombol untuk demo
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _showPosisiDialog,
                    child: Text('Pilih Posisi'),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton(
                    onPressed: _clearSelection,
                    child: Text('Reset'),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 24),
            
            // Tampilkan data yang dipilih
            if (selectedPosisi != null) ...[
              Text(
                'Data Terpilih:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('ID: ${selectedPosisi!.id}'),
                    Text('Nama: ${selectedPosisi!.text}'),
                    Text('Status: ${selectedPosisi!.id.startsWith('temp_') ? 'Posisi Baru' : 'Dari Database'}'),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showPosisiDialog() async {
    final result = await showModalBottomSheet<PosisiPekerjaanModel>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const PosisiPekerjaanDialog(),
    );

    if (result != null) {
      setState(() {
        selectedPosisi = result;
        _posisiController.text = result.text;
      });

      // Tampilkan feedback
      Get.snackbar(
        'Posisi Dipilih',
        result.id.startsWith('temp_') 
            ? 'Posisi baru "${result.text}" berhasil ditambahkan!'
            : 'Posisi "${result.text}" dipilih',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: result.id.startsWith('temp_') 
            ? Colors.green 
            : Colors.blue,
        colorText: Colors.white,
      );
    }
  }

  void _clearSelection() {
    setState(() {
      selectedPosisi = null;
      _posisiController.clear();
    });
  }

  @override
  void dispose() {
    _posisiController.dispose();
    super.dispose();
  }
}
